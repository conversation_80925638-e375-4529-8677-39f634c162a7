import { Outlet, Navigate } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import styled from 'styled-components';
import { useAuth } from '../../context/AuthContext';

const Main = styled.main`
  min-height: calc(100vh - 80px - 350px); /* Adjust based on header and footer height */
`;

const Layout = () => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  return (
    <>
      <Header />
      <Main>
        <Outlet />
      </Main>
      <Footer />
    </>
  );
};

export default Layout;
