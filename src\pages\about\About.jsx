import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faGlobe,
  faHandshake,
  faLightbulb,
  faArrowRight,
  faArrowLeft
} from '@fortawesome/free-solid-svg-icons';

import Hero from '../../components/ui/Hero';
import Section from '../../components/ui/Section';
import Button from '../../components/ui/Button';

// Custom styled components for consistent styling
const SectionTitle = styled.h2`
  font-size: 4.5rem;
  margin-bottom: var(--spacing-md);
  color: var(--dark-color);

  @media (max-width: 992px) {
    font-size: 3.5rem;
  }

  @media (max-width: 768px) {
    font-size: 3rem;
  }

  @media (max-width: 576px) {
    font-size: 2.5rem;
  }
`;

const Paragraph = styled.p`
  font-size: 1.4rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }

  @media (max-width: 576px) {
    font-size: 1.1rem;
  }
`;

// Import placeholder images
import images from '../../assets/placeholderImages';

const MissionContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
  background-color: #f8f9fa; /* Light gray background */

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const MissionContent = styled.div`
  background-color: #ffffff; /* Pure white */
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

  @media (max-width: 992px) {
    order: 1;
  }
`;

const MissionImage = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: var(--spacing-md);

  @media (max-width: 992px) {
    order: 2;
    margin-top: var(--spacing-xl);
  }

  img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }
  }
`;

const ValuesContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ValueCard = styled.div`
  background-color: #ffffff; /* Pure white */
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const ValueHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
`;

const ValueIcon = styled.div`
  font-size: 2rem;
  color: var(--primary-color);
  margin-right: var(--spacing-md);
`;

const ValueTitle = styled.h3`
  font-size: xx-large;
  color: var(--dark-color);
  margin: 0;
  font-weight: 600;
`;

const TeamContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-lg);

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const TeamMember = styled.div`
  text-align: center;
  background-color: #ffffff; /* Pure white */
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const TeamMemberImage = styled.img`
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
`;

const TeamMemberName = styled.h3`
  font-size: large;
  margin-bottom: var(--spacing-xs);
`;

const TeamMemberRole = styled.p`
  color: var(--text-light);
  margin-bottom: var(--spacing-sm);
`;

const TeamMemberBio = styled.p`
  font-size: 1.2rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
`;

// Exchange Philosophy Section Styles
const PhilosophyContainer = styled.div`
  background-color: #ffffff; /* Pure white */
  color: var(--text-color);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
`;

const PhilosophyTitle = styled.h2`
  text-align: center;
  margin-bottom: var(--spacing-lg);
  font-size: 4.5rem;
  color: var(--dark-color);

  @media (max-width: 992px) {
    font-size: 3.5rem;
  }

  @media (max-width: 768px) {
    font-size: 3rem;
  }

  @media (max-width: 576px) {
    font-size: 2.5rem;
  }
`;

const PhilosophyUnderline = styled.div`
  width: 100px;
  height: 4px;
  background-color: #a3e635;
  margin: 0 auto;
  margin-bottom: var(--spacing-xl);
`;

const PhilosophyIntro = styled.p`
  font-size: 1.4rem;
  margin-bottom: var(--spacing-xl);
  max-width: 800px;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }

  @media (max-width: 576px) {
    font-size: 1.1rem;
  }
`;

const PhilosophyContent = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
`;

const PhilosophyPoints = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

const PhilosophyPoint = styled.div`
  background-color: #f8f9fa; /* Light gray background */
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 1.4rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    font-size: 1.2rem;
  }

  @media (max-width: 576px) {
    font-size: 1.1rem;
    padding: var(--spacing-md);
  }
`;

const PhilosophyArrows = styled.div`
  display: flex;
  color: #a3e635;
  font-size: 1.5rem;
  margin-right: var(--spacing-md);
`;

const PhilosophyImage = styled.div`
  background-color: #f8f9fa; /* Light gray background */
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

  img {
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  @media (max-width: 992px) {
    margin-top: var(--spacing-xl);
  }
`;

const About = () => {
  return (
    <>
      <Hero
        bgImage={images.heroAbout}
        title="About SkillSwap"
        subtitle="Learn more about our mission, values, and the team behind SkillSwap."
        height="60vh"
      />

      <Section bgColor="#f8f9fa">
        <MissionContainer>
          <MissionContent>
            <SectionTitle>Our Mission</SectionTitle>
            <Paragraph>
              At SkillSwap, our mission is to democratize knowledge sharing and create a global community
              where everyone can learn from each other. We believe that everyone has valuable skills and
              knowledge to share, and we're committed to making skill sharing accessible, enjoyable, and
              rewarding for all.
            </Paragraph>
            <Paragraph>
              We're building a platform that connects people who want to learn with those who want to teach,
              breaking down barriers to education and creating opportunities for personal and professional growth.
            </Paragraph>
          </MissionContent>

          <MissionImage>
            <img src={images.teamMember1} alt="Team member" />
            <img src={images.teamMember2} alt="Team member" />
            <img src={images.teamMember3} alt="Team member" />
            <img src={images.teamMember4} alt="Team member" />
          </MissionImage>
        </MissionContainer>
      </Section>

      <Section
        title="Our Values"
        subtitle="These core principles guide everything we do at SkillSwap."
        bgColor="#f8f9fa"
      >
        <ValuesContainer>
          <ValueCard>
            <ValueHeader>
              <ValueIcon>
                <FontAwesomeIcon icon={faUsers} />
              </ValueIcon>
              <ValueTitle>Community First</ValueTitle>
            </ValueHeader>
            <Paragraph>
              We believe in the power of community. By bringing together diverse individuals with
              different skills and backgrounds, we create a rich learning environment where everyone
              can grow and thrive.
            </Paragraph>
          </ValueCard>

          <ValueCard>
            <ValueHeader>
              <ValueIcon>
                <FontAwesomeIcon icon={faGlobe} />
              </ValueIcon>
              <ValueTitle>Accessibility</ValueTitle>
            </ValueHeader>
            <Paragraph>
              Knowledge should be accessible to everyone. We're committed to breaking down barriers
              to education and making skill sharing available to people from all walks of life,
              regardless of their background or circumstances.
            </Paragraph>
          </ValueCard>

          <ValueCard>
            <ValueHeader>
              <ValueIcon>
                <FontAwesomeIcon icon={faHandshake} />
              </ValueIcon>
              <ValueTitle>Mutual Benefit</ValueTitle>
            </ValueHeader>
            <Paragraph>
              We create win-win situations where both learners and teachers benefit. Learners gain
              valuable skills, while teachers can share their passion, refine their own understanding,
              and earn rewards for their expertise.
            </Paragraph>
          </ValueCard>

          <ValueCard>
            <ValueHeader>
              <ValueIcon>
                <FontAwesomeIcon icon={faLightbulb} />
              </ValueIcon>
              <ValueTitle>Continuous Innovation</ValueTitle>
            </ValueHeader>
            <Paragraph>
              We're constantly looking for new ways to improve the learning and teaching experience.
              By embracing innovation and staying at the forefront of educational technology, we can
              better serve our community.
            </Paragraph>
          </ValueCard>
        </ValuesContainer>
      </Section>

      <Section bgColor="#f8f9fa">
        <PhilosophyContainer>
          <PhilosophyTitle>Exchange Philosophy</PhilosophyTitle>
          <PhilosophyUnderline />

          <PhilosophyIntro>
            We believe that skill exchange surpasses traditional learning for several reasons:
          </PhilosophyIntro>

          <PhilosophyContent>
            <PhilosophyPoints>
              <PhilosophyPoint>
                <PhilosophyArrows>
                  <FontAwesomeIcon icon={faArrowRight} />
                  <FontAwesomeIcon icon={faArrowLeft} />
                </PhilosophyArrows>
                Mutual benefit - both participants receive value
              </PhilosophyPoint>

              <PhilosophyPoint>
                <PhilosophyArrows>
                  <FontAwesomeIcon icon={faArrowRight} />
                  <FontAwesomeIcon icon={faArrowLeft} />
                </PhilosophyArrows>
                Practical knowledge from practicing professionals
              </PhilosophyPoint>

              <PhilosophyPoint>
                <PhilosophyArrows>
                  <FontAwesomeIcon icon={faArrowRight} />
                  <FontAwesomeIcon icon={faArrowLeft} />
                </PhilosophyArrows>
                Building community and social connections
              </PhilosophyPoint>

              <PhilosophyPoint>
                <PhilosophyArrows>
                  <FontAwesomeIcon icon={faArrowRight} />
                  <FontAwesomeIcon icon={faArrowLeft} />
                </PhilosophyArrows>
                Accessibility for everyone, regardless of financial capabilities
              </PhilosophyPoint>
            </PhilosophyPoints>

            <PhilosophyImage>
              <img src={images.exchangePhilosophy} alt="Learning concept illustration" />
            </PhilosophyImage>
          </PhilosophyContent>
        </PhilosophyContainer>
      </Section>

      <Section
        title="Meet Our Team"
        subtitle="The passionate individuals behind SkillSwap who are dedicated to our mission."
        bgColor="#f8f9fa"
      >
        <TeamContainer>
          <TeamMember>
            <TeamMemberImage src={images.teamMember1} alt="Sarah Johnson" />
            <TeamMemberName>Sarah Johnson</TeamMemberName>
            <TeamMemberRole>CEO & Co-Founder</TeamMemberRole>
            <TeamMemberBio>
              Sarah is passionate about education and believes in the power of skill sharing to transform lives.
            </TeamMemberBio>
          </TeamMember>

          <TeamMember>
            <TeamMemberImage src={images.teamMember2} alt="Michael Chen" />
            <TeamMemberName>Michael Chen</TeamMemberName>
            <TeamMemberRole>CTO & Co-Founder</TeamMemberRole>
            <TeamMemberBio>
              Michael brings his technical expertise to build a platform that connects people around the world.
            </TeamMemberBio>
          </TeamMember>

          <TeamMember>
            <TeamMemberImage src={images.teamMember3} alt="Emily Rodriguez" />
            <TeamMemberName>Emily Rodriguez</TeamMemberName>
            <TeamMemberRole>Head of Community</TeamMemberRole>
            <TeamMemberBio>
              Emily works to create a vibrant and supportive community where everyone feels welcome.
            </TeamMemberBio>
          </TeamMember>

          <TeamMember>
            <TeamMemberImage src={images.teamMember4} alt="David Kim" />
            <TeamMemberName>David Kim</TeamMemberName>
            <TeamMemberRole>Head of Product</TeamMemberRole>
            <TeamMemberBio>
              David focuses on creating an intuitive and enjoyable experience for both learners and teachers.
            </TeamMemberBio>
          </TeamMember>
        </TeamContainer>
      </Section>

      <Section
        bgColor="var(--primary-color)"
        spacing="lg"
      >
        <div style={{ textAlign: 'center', maxWidth: '700px', margin: '0 auto' }}>
          <SectionTitle style={{ color: 'white', marginBottom: 'var(--spacing-lg)' }}>Join Our Community Today</SectionTitle>
          <Paragraph style={{ color: 'white', marginBottom: 'var(--spacing-xl)' }}>
            Whether you want to learn new skills or share your expertise, SkillSwap is the perfect platform for you.
          </Paragraph>
          <Button
            as={Link}
            to="/create-account"
            variant="secondary"
            size="large"
            style={{ marginRight: 'var(--spacing-md)' }}
          >
            Sign Up Now
          </Button>
          <Button
            as={Link}
            to="/login"
            variant="outline"
            size="large"
            style={{ color: 'white', borderColor: 'white' }}
          >
            Login
          </Button>
        </div>
      </Section>
    </>
  );
};

export default About;
