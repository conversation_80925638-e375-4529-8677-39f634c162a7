import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCalendarAlt,
  faChevronLeft,
  faChevronRight,
  faPlus,
  faClock,
  faMapMarkerAlt,
  faVideo,
  faUserCircle,
  faCheckCircle,
  faTimesCircle,
  faEllipsisV
} from '@fortawesome/free-solid-svg-icons';

// Styled Components
const ScheduleContainer = styled.div`
  background-color: #121212;
  color: white;
  min-height: calc(100vh - 80px);
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const ScheduleHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const ScheduleTitle = styled.h1`
  font-size: 2rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
  svg {
    color: #5fe3e4;
  }
`;

const ScheduleActions = styled.div`
  display: flex;
  gap: 1rem;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
`;

const ActionButton = styled.button`
  background-color: #212121;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: #2a2a2a;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  }
  
  svg {
    color: #5fe3e4;
  }
`;

const PrimaryButton = styled(ActionButton)`
  background-color: #004d40;
  
  &:hover {
    background-color: #00695c;
  }
`;

const ScheduleContent = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const CalendarSidebar = styled.div`
  background-color: #212121;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: fit-content;
  
  @media (max-width: 1024px) {
    order: 2;
  }
`;

const CalendarHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const MonthSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  button {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: all 0.2s;
    
    &:hover {
      background-color: #333;
      color: white;
    }
  }
  
  h3 {
    font-size: 1.2rem;
    color: white;
    min-width: 120px;
    text-align: center;
  }
`;

const WeekdaysRow = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 0.5rem;
  
  span {
    font-size: 0.8rem;
    color: #999;
    padding: 0.5rem 0;
  }
`;

const DaysGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
`;

const DayCell = styled.div`
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  font-size: 0.9rem;
  color: ${props => props.isCurrentMonth ? 'white' : '#666'};
  background-color: ${props => props.isSelected ? '#004d40' : 'transparent'};
  font-weight: ${props => props.isToday ? '700' : '400'};
  
  &:hover {
    background-color: ${props => props.isSelected ? '#00695c' : '#333'};
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 3px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: ${props => props.hasEvents ? '#5fe3e4' : 'transparent'};
  }
`;

const UpcomingEvents = styled.div`
  margin-top: 2rem;
  
  h3 {
    font-size: 1.1rem;
    color: white;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    svg {
      color: #5fe3e4;
      font-size: 1rem;
    }
  }
`;

const EventsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const EventItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #2a2a2a;
  border-radius: 8px;
  transition: all 0.2s;
  
  &:hover {
    background-color: #333;
    transform: translateY(-2px);
  }
`;

const EventColor = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${props => props.color};
  flex-shrink: 0;
`;

const EventInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const EventTitle = styled.div`
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const EventTime = styled.div`
  font-size: 0.8rem;
  color: #bbb;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const ScheduleMain = styled.div`
  @media (max-width: 1024px) {
    order: 1;
  }
`;

const DateHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    font-size: 1.5rem;
    color: white;
  }
`;

const TimeSlots = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const TimeSlot = styled.div`
  display: flex;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const TimeLabel = styled.div`
  width: 80px;
  text-align: right;
  padding-top: 0.5rem;
  font-size: 0.9rem;
  color: #999;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    width: auto;
    text-align: left;
    padding-top: 0;
    margin-bottom: 0.5rem;
  }
`;

const SlotContent = styled.div`
  flex: 1;
  min-height: 80px;
  border-left: 2px solid #333;
  padding-left: 1.5rem;
  position: relative;
  
  @media (max-width: 768px) {
    border-left: none;
    border-top: 2px solid #333;
    padding-left: 0;
    padding-top: 1rem;
  }
  
  &::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.75rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #333;
    
    @media (max-width: 768px) {
      left: 0;
      top: -6px;
    }
  }
`;

const EventCard = styled.div`
  background-color: ${props => props.color || '#212121'};
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-bottom: 1rem;
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  }
`;

const EventHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
`;

const EventTitleMain = styled.h3`
  font-size: 1.1rem;
  color: white;
  margin-bottom: 0.5rem;
`;

const EventActions = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  cursor: pointer;
  
  &:hover {
    color: #5fe3e4;
  }
`;

const EventDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const EventDetail = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  
  svg {
    color: rgba(255, 255, 255, 0.6);
    width: 16px;
  }
`;

const ParticipantsList = styled.div`
  display: flex;
  margin-top: 1rem;
`;

const Participant = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid ${props => props.color || '#212121'};
  margin-left: -8px;
  
  &:first-child {
    margin-left: 0;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const EmptySlot = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 80px;
  color: #666;
  border: 2px dashed #333;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: #5fe3e4;
    color: #5fe3e4;
  }
  
  svg {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }
  
  span {
    font-size: 0.9rem;
  }
`;

// Sample data
const months = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const events = [
  {
    id: 1,
    title: 'Python Basics Tutorial',
    date: new Date(2023, 6, 15),
    startTime: '10:00 AM',
    endTime: '11:30 AM',
    location: 'Online (Zoom)',
    type: 'Teaching',
    color: '#004d40',
    participants: [
      { id: 'anil42', avatar: 'https://randomuser.me/api/portraits/men/32.jpg' }
    ],
    description: 'Introduction to Python programming language basics.'
  },
  {
    id: 2,
    title: 'React Components Workshop',
    date: new Date(2023, 6, 15),
    startTime: '2:00 PM',
    endTime: '4:00 PM',
    location: 'Online (Google Meet)',
    type: 'Learning',
    color: '#4db6ac',
    participants: [
      { id: 'ravi75', avatar: 'https://randomuser.me/api/portraits/men/68.jpg' }
    ],
    description: 'Learning about React component architecture and best practices.'
  },
  {
    id: 3,
    title: 'UI Design Principles',
    date: new Date(2023, 6, 17),
    startTime: '11:00 AM',
    endTime: '12:30 PM',
    location: 'Coffee Shop, Downtown',
    type: 'Learning',
    color: '#4db6ac',
    participants: [
      { id: 'priya23', avatar: 'https://randomuser.me/api/portraits/women/65.jpg' }
    ],
    description: 'Learning about fundamental UI design principles and practices.'
  },
  {
    id: 4,
    title: 'Data Science Fundamentals',
    date: new Date(2023, 6, 20),
    startTime: '3:00 PM',
    endTime: '5:00 PM',
    location: 'Online (Zoom)',
    type: 'Teaching',
    color: '#004d40',
    participants: [
      { id: 'john55', avatar: 'https://randomuser.me/api/portraits/men/55.jpg' },
      { id: 'sarah22', avatar: 'https://randomuser.me/api/portraits/women/22.jpg' }
    ],
    description: 'Teaching the basics of data science and analysis techniques.'
  }
];

// Helper functions
const getDaysInMonth = (year, month) => {
  return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfMonth = (year, month) => {
  return new Date(year, month, 1).getDay();
};

const isSameDay = (date1, date2) => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

const Schedule = () => {
  const { userId } = useParams();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [calendarDays, setCalendarDays] = useState([]);
  const [dailyEvents, setDailyEvents] = useState([]);
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  
  // Generate calendar days
  useEffect(() => {
    const days = [];
    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDay = getFirstDayOfMonth(currentYear, currentMonth);
    
    // Previous month days
    const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const daysInPrevMonth = getDaysInMonth(prevYear, prevMonth);
    
    for (let i = firstDay - 1; i >= 0; i--) {
      days.push({
        day: daysInPrevMonth - i,
        month: prevMonth,
        year: prevYear,
        isCurrentMonth: false
      });
    }
    
    // Current month days
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        day: i,
        month: currentMonth,
        year: currentYear,
        isCurrentMonth: true
      });
    }
    
    // Next month days
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;
    const remainingDays = 42 - days.length; // 6 rows of 7 days
    
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        day: i,
        month: nextMonth,
        year: nextYear,
        isCurrentMonth: false
      });
    }
    
    setCalendarDays(days);
  }, [currentMonth, currentYear]);
  
  // Filter events for selected date
  useEffect(() => {
    const filteredEvents = events.filter(event => 
      isSameDay(event.date, selectedDate)
    );
    
    // Sort by start time
    filteredEvents.sort((a, b) => {
      const timeA = new Date(`1970/01/01 ${a.startTime}`);
      const timeB = new Date(`1970/01/01 ${b.startTime}`);
      return timeA - timeB;
    });
    
    setDailyEvents(filteredEvents);
    
    // Get upcoming events (next 3 events from today)
    const today = new Date();
    const upcoming = events
      .filter(event => event.date >= today)
      .sort((a, b) => a.date - b.date)
      .slice(0, 3);
    
    setUpcomingEvents(upcoming);
  }, [selectedDate]);
  
  const handlePrevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };
  
  const handleNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };
  
  const handleDateSelect = (day) => {
    const newDate = new Date(day.year, day.month, day.day);
    setSelectedDate(newDate);
  };
  
  const hasEvents = (day) => {
    return events.some(event => 
      event.date.getFullYear() === day.year &&
      event.date.getMonth() === day.month &&
      event.date.getDate() === day.day
    );
  };
  
  const isToday = (day) => {
    const today = new Date();
    return today.getFullYear() === day.year &&
           today.getMonth() === day.month &&
           today.getDate() === day.day;
  };
  
  const formatDate = (date) => {
    const options = { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };
  
  // Generate time slots from 8 AM to 8 PM
  const timeSlots = [];
  for (let i = 8; i <= 20; i++) {
    const hour = i > 12 ? i - 12 : i;
    const ampm = i >= 12 ? 'PM' : 'AM';
    timeSlots.push(`${hour}:00 ${ampm}`);
  }
  
  // Find events for a specific time slot
  const getEventsForTimeSlot = (timeSlot) => {
    return dailyEvents.filter(event => {
      const eventStart = new Date(`1970/01/01 ${event.startTime}`);
      const slotTime = new Date(`1970/01/01 ${timeSlot}`);
      const eventEnd = new Date(`1970/01/01 ${event.endTime}`);
      
      // Check if the event starts at this time slot or is ongoing during this slot
      return (
        (eventStart.getHours() === slotTime.getHours()) ||
        (eventStart < slotTime && eventEnd > slotTime)
      );
    });
  };
  
  return (
    <ScheduleContainer>
      <ScheduleHeader>
        <ScheduleTitle>
          <FontAwesomeIcon icon={faCalendarAlt} />
          Schedule
        </ScheduleTitle>
        
        <ScheduleActions>
          <ActionButton>
            <FontAwesomeIcon icon={faCalendarAlt} />
            My Calendar
          </ActionButton>
          <PrimaryButton>
            <FontAwesomeIcon icon={faPlus} />
            New Session
          </PrimaryButton>
        </ScheduleActions>
      </ScheduleHeader>
      
      <ScheduleContent>
        <CalendarSidebar>
          <CalendarHeader>
            <MonthSelector>
              <button onClick={handlePrevMonth}>
                <FontAwesomeIcon icon={faChevronLeft} />
              </button>
              <h3>{months[currentMonth]} {currentYear}</h3>
              <button onClick={handleNextMonth}>
                <FontAwesomeIcon icon={faChevronRight} />
              </button>
            </MonthSelector>
          </CalendarHeader>
          
          <WeekdaysRow>
            {weekdays.map(day => (
              <span key={day}>{day}</span>
            ))}
          </WeekdaysRow>
          
          <DaysGrid>
            {calendarDays.map((day, index) => (
              <DayCell 
                key={index}
                isCurrentMonth={day.isCurrentMonth}
                isSelected={isSameDay(selectedDate, new Date(day.year, day.month, day.day))}
                isToday={isToday(day)}
                hasEvents={hasEvents(day)}
                onClick={() => handleDateSelect(day)}
              >
                {day.day}
              </DayCell>
            ))}
          </DaysGrid>
          
          <UpcomingEvents>
            <h3>
              <FontAwesomeIcon icon={faCalendarAlt} />
              Upcoming Sessions
            </h3>
            
            <EventsList>
              {upcomingEvents.length > 0 ? (
                upcomingEvents.map(event => (
                  <EventItem key={event.id}>
                    <EventColor color={event.color} />
                    <EventInfo>
                      <EventTitle>{event.title}</EventTitle>
                      <EventTime>
                        <FontAwesomeIcon icon={faClock} />
                        {new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} • {event.startTime}
                      </EventTime>
                    </EventInfo>
                  </EventItem>
                ))
              ) : (
                <div style={{ color: '#999', textAlign: 'center', padding: '1rem 0' }}>
                  No upcoming sessions
                </div>
              )}
            </EventsList>
          </UpcomingEvents>
        </CalendarSidebar>
        
        <ScheduleMain>
          <DateHeader>
            <h2>{formatDate(selectedDate)}</h2>
          </DateHeader>
          
          <TimeSlots>
            {timeSlots.map((timeSlot, index) => {
              const slotEvents = getEventsForTimeSlot(timeSlot);
              
              return (
                <TimeSlot key={index}>
                  <TimeLabel>{timeSlot}</TimeLabel>
                  <SlotContent>
                    {slotEvents.length > 0 ? (
                      slotEvents.map(event => (
                        <EventCard key={event.id} color={event.color}>
                          <EventHeader>
                            <div>
                              <EventTitleMain>{event.title}</EventTitleMain>
                              <EventTime>
                                <FontAwesomeIcon icon={faClock} />
                                {event.startTime} - {event.endTime}
                              </EventTime>
                            </div>
                            <EventActions>
                              <FontAwesomeIcon icon={faEllipsisV} />
                            </EventActions>
                          </EventHeader>
                          
                          <EventDetails>
                            <EventDetail>
                              <FontAwesomeIcon icon={faMapMarkerAlt} />
                              {event.location}
                            </EventDetail>
                            {event.type === 'Teaching' ? (
                              <EventDetail>
                                <FontAwesomeIcon icon={faCheckCircle} />
                                Teaching Session
                              </EventDetail>
                            ) : (
                              <EventDetail>
                                <FontAwesomeIcon icon={faLightbulb} />
                                Learning Session
                              </EventDetail>
                            )}
                            {event.description && (
                              <EventDetail style={{ marginTop: '0.5rem' }}>
                                {event.description}
                              </EventDetail>
                            )}
                          </EventDetails>
                          
                          {event.participants && event.participants.length > 0 && (
                            <ParticipantsList>
                              {event.participants.map(participant => (
                                <Participant key={participant.id} color={event.color}>
                                  <img src={participant.avatar} alt="Participant" />
                                </Participant>
                              ))}
                            </ParticipantsList>
                          )}
                        </EventCard>
                      ))
                    ) : (
                      <EmptySlot>
                        <FontAwesomeIcon icon={faPlus} />
                        <span>Add Session</span>
                      </EmptySlot>
                    )}
                  </SlotContent>
                </TimeSlot>
              );
            })}
          </TimeSlots>
        </ScheduleMain>
      </ScheduleContent>
    </ScheduleContainer>
  );
};

export default Schedule;
