import styled, { css } from 'styled-components';

const ButtonStyles = css`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: var(--font-size-md);
  border: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  ${props => props.size === 'small' && css`
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
  `}
  
  ${props => props.size === 'large' && css`
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
  `}
  
  ${props => props.fullWidth && css`
    width: 100%;
  `}
`;

const PrimaryButton = styled.button`
  ${ButtonStyles}
  background-color: var(--primary-color);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--primary-color);
    filter: brightness(110%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
`;

const SecondaryButton = styled.button`
  ${ButtonStyles}
  background-color: var(--secondary-color);
  color: var(--dark-color);
  
  &:hover:not(:disabled) {
    background-color: var(--secondary-color);
    filter: brightness(105%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
`;

const OutlineButton = styled.button`
  ${ButtonStyles}
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  
  &:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
`;

const TextButton = styled.button`
  ${ButtonStyles}
  background-color: transparent;
  color: var(--primary-color);
  padding: 0.5rem 0.75rem;
  
  &:hover:not(:disabled) {
    background-color: rgba(106, 61, 232, 0.1);
  }
`;

const Button = ({ variant = 'primary', children, ...props }) => {
  switch (variant) {
    case 'secondary':
      return <SecondaryButton {...props}>{children}</SecondaryButton>;
    case 'outline':
      return <OutlineButton {...props}>{children}</OutlineButton>;
    case 'text':
      return <TextButton {...props}>{children}</TextButton>;
    default:
      return <PrimaryButton {...props}>{children}</PrimaryButton>;
  }
};

export default Button;
