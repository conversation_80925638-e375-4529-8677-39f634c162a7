# SkillSwap

SkillSwap is a platform dedicated to connecting people who want to learn with those who want to teach. The platform facilitates skill exchange, enabling users to share knowledge and learn from others in a community-based environment.

![SkillSwap Logo](src/assets/logo.png)

## 🌟 Features

- **User Authentication**: Register, login, and manage user profiles
- **Skill Discovery**: Browse and search for skills you want to learn
- **Skill Sharing**: Offer your expertise to teach others
- **Community Building**: Connect with like-minded individuals
- **Profile Management**: Showcase your skills, education, and experience
- **Scheduling**: Arrange skill exchange sessions with other users
- **Responsive Design**: Fully responsive interface that works on all devices

## 🚀 Technologies Used

- **Frontend**: React.js, Styled Components
- **UI Components**: Custom UI components
- **Icons**: Font Awesome
- **Routing**: React Router
- **State Management**: React Hooks (useState, useEffect)
- **Build Tool**: Vite

## 📋 Pages

- **Home**: Landing page with platform overview and key features
- **About**: Information about SkillSwap's mission and values
- **Discover**: Browse and find skills and users
- **Services**: Explore available skill categories and offerings
- **Contact**: Get in touch with the SkillSwap team
- **Profile**: User profiles with skills, education, and experience
- **Register/Login**: User authentication pages

## 🎨 Design System

- **Color Scheme**: Light gray (#f8f9fa) background, pure white for cards, primary blue for accents
- **Typography**: Clean, readable fonts with varying sizes for different sections
- **Components**: Reusable UI components for buttons, cards, sections, etc.
- **Spacing**: Consistent spacing system with predefined variables

## 🔧 Setup and Installation

### Prerequisites

- Node.js (v14.0.0 or later)
- npm or yarn

### Installation Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/Hareshku/skillswap-front-end.git
   cd skillswap-front-end
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:5173
   ```

## 📱 Responsive Design

SkillSwap is designed to work seamlessly across all device sizes:
- Desktop
- Tablet
- Mobile

The layout adjusts automatically to provide the best user experience regardless of screen size.

## 🔄 Application Flow

### User Journey Flow Chart

```
+---------------------+     +----------------------+     +---------------------+
|                     |     |                      |     |                     |
| User Visits Website +---->+ Login/Register Page  +---->+ Authentication      |
|                     |     |                      |     |                     |
+---------------------+     +----------------------+     +---------+-----------+
                                                                  |
                                                                  v
+---------------------+     +----------------------+     +---------------------+
|                     |     |                      |     |                     |
| Skill Exchange      +<----+ Connect with Users   +<----+ Browse Discover    |
| Process             |     |                      |     | Page                |
+-----+---------------+     +----------------------+     +---------------------+
      |
      v
+---------------------+     +----------------------+     +---------------------+
|                     |     |                      |     |                     |
| Schedule Sessions   +---->+ Exchange Skills      +---->+ Provide Feedback   |
|                     |     |                      |     |                     |
+---------------------+     +----------------------+     +---------------------+
```

### Application Architecture Flow

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
| User Interface    +---->+ React Components  +---->+ React Router     |
| (Pages)           |     | (UI Elements)     |     | (Navigation)     |
+-------------------+     +-------------------+     +-------------------+
         |                         |                         |
         v                         v                         v
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
| State Management  +---->+ User Data         +---->+ Skills Data      |
| (React Hooks)     |     | (Profiles)        |     | (Categories)     |
+-------------------+     +-------------------+     +-------------------+
```

### Detailed System Flow

```
                                  +-------------+
                                  |             |
                                  |   START     |
                                  |             |
                                  +------+------+
                                         |
                                         v
                                  +------+------+
                                  |             |
                                  | Login Page  |
                                  |             |
                                  +------+------+
                                         |
                 +------------------------+------------------------+
                 |                        |                       |
                 v                        v                       v
          +------+------+         +------+------+         +------+------+
          |             |         |             |         |             |
          |   Login     |         |  Register   |         |   Admin     |
          |             |         |             |         |   Login     |
          +------+------+         +------+------+         +------+------+
                 |                        |                       |
                 v                        v                       v
          +------+------+         +------+------+         +------+------+
          |             |         |             |         |             |
          | Home Page   |         | Registration|         | Admin       |
          |             |         | Form        |         | Dashboard   |
          +------+------+         +------+------+         +------+------+
                 |                        |                       |
                 |                        v                       |
                 |                 +------+------+               |
                 |                 |             |               |
                 +---------------->| User Profile|<--------------+
                                   |             |
                                   +------+------+
                                          |
          +---------------------------+---+---+---------------------------+
          |                           |       |                           |
          v                           v       v                           v
   +------+------+            +------+------+            +------+------+
   |             |            |             |            |             |
   | Discover    |            | Services    |            | Contact     |
   | Page        |            | Page        |            | Page        |
   +------+------+            +------+------+            +-------------+
          |                           |
          v                           v
   +------+------+            +------+------+
   |             |            |             |
   | Connect with|            | View Skill  |
   | Users       |            | Details     |
   +------+------+            +------+------+
          |                           |
          +---------------------------+
                                      |
                                      v
                               +------+------+
                               |             |
                               | Skill       |
                               | Exchange    |
                               +------+------+
                                      |
                                      v
                               +------+------+
                               |             |
                               |    END      |
                               |             |
                               +-------------+
```

These diagrams illustrate the user journey through the SkillSwap platform and the application's architecture.

## 👥 User Roles

- **Regular Users**: Can create profiles, discover skills, and connect with others
- **Admin**: Access to admin panel for platform management (username: admin, password: admin12)

### Admin Privileges

The Admin Panel provides the following capabilities:

1. **User Management**
   - Approve, block, or suspend users when necessary
   - Review and respond to user-submitted reports or complaints

2. **Skill Listings Moderation**
   - Approve, edit, or remove skill listings that violate platform guidelines
   - Verify certifications for skills that require them before publishing
   - Only approved skills are visible to other users

## 📊 Database Schema

The platform will use a relational database with the following five key tables:

### Core Tables

1. **Users**
   - Purpose: Stores user profiles and login information
   - Key fields: id, username, email, password, name, avatar, bio, status
   - Relationships: One-to-many with User_Offered_Skills and User_Desired_Skills

2. **Skills**
   - Purpose: Defines all available skills on the platform
   - Key fields: id, name, category, description, requires_certification
   - Relationships: One-to-many with User_Offered_Skills and User_Desired_Skills

3. **User_Offered_Skills**
   - Purpose: Tracks what skills users can teach
   - Key fields: id, user_id, skill_id, proficiency_level, status
   - Relationships:
     - Many-to-one with Users (each offered skill belongs to one user)
     - Many-to-one with Skills (each offered skill references one skill)
     - One-to-many with Matches (as the teaching side)

4. **User_Desired_Skills**
   - Purpose: Tracks what skills users want to learn
   - Key fields: id, user_id, skill_id, interest_level, status
   - Relationships:
     - Many-to-one with Users (each desired skill belongs to one user)
     - Many-to-one with Skills (each desired skill references one skill)
     - One-to-many with Matches (as the learning side)

5. **Matches**
   - Purpose: Records successful skill pairings between users
   - Key fields: id, teacher_skill_id, learner_skill_id, status, created_at
   - Relationships:
     - Many-to-one with User_Offered_Skills (teacher side)
     - Many-to-one with User_Desired_Skills (learner side)

### Table Connectivity Diagram

```
+----------------+       +-------------------+       +----------------+
|                |       |                   |       |                |
|     Users      |<----->| User_Offered_Skills |<----->|    Skills     |
|                |       |                   |       |                |
+----------------+       +--------+----------+       +----------------+
        ^                         |                          ^
        |                         |                          |
        |                         v                          |
        |                +-----------------+                 |
        |                |                 |                 |
        |                |     Matches     |                 |
        |                |                 |                 |
        |                +--------+--------+                 |
        |                         |                          |
        |                         |                          |
        |                         v                          |
        |                +--------+----------+               |
        |                |                   |               |
        +--------------->| User_Desired_Skills |<--------------+
                         |                   |
                         +-------------------+
```

### Key Relationships

- A **User** can offer multiple skills (User_Offered_Skills) and desire multiple skills (User_Desired_Skills)
- Each **Skill** can be offered by multiple users and desired by multiple users
- A **Match** connects one User_Offered_Skill with one User_Desired_Skill, creating a teaching/learning relationship
- This structure enables the core functionality of matching users who can teach skills with users who want to learn those skills

For a more detailed database schema with all columns and additional tables, see [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md).

## 🔜 Future Enhancements

- Backend integration for persistent data storage
- Real-time chat functionality
- Advanced search and filtering options
- Rating and review system
- Payment integration for premium services
- Community forums and discussion boards

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Contact

For any inquiries, please reach out to us at:
- **Location**: Rashidi hostel MUET, Jamshoro
- **Phone**: 03065805656
- **Email**: <EMAIL>

---

© 2023 SkillSwap. All rights reserved.
