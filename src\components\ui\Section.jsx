import styled, { css } from 'styled-components';

const SectionContainer = styled.section`
  min-height: 100vh; // Take full viewport height
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: var(--spacing-xl) 0;
  position: relative;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 1;
  margin-bottom: 100px; // Add 100px space between sections

  ${props => props.bgColor && css`
    background-color: ${props.bgColor};
  `}

  ${props => props.bgImage && css`
    background-image: url(${props.bgImage});
    background-size: cover;
    background-position: center;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, ${props.overlayOpacity || 0.5});
      z-index: 1;
    }

    & > * {
      position: relative;
      z-index: 2;
    }
  `}

  ${props => props.spacing === 'sm' && css`
    padding: var(--spacing-xl) 0;
  `}

  ${props => props.spacing === 'lg' && css`
    padding: var(--spacing-3xl) 0;
  `}
`;

const SectionContent = styled.div`
  width: calc(100% - 6rem);
  margin: 0 3rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  ${props => props.narrow && css`
    max-width: 100%;
  `}
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: var(--spacing-2xl);

  ${props => props.alignment === 'left' && css`
    text-align: left;
  `}

  ${props => props.alignment === 'right' && css`
    text-align: right;
  `}
`;

const SectionTitle = styled.h2`
  font-size: var(--font-size-6xl);
  color: ${props => props.color || 'var(--dark-color)'};
  margin-bottom: var(--spacing-md);

  @media (max-width: 992px) {
    font-size: var(--font-size-5xl);
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
  }

  @media (max-width: 576px) {
    font-size: var(--font-size-3xl);
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.5rem;
  color: ${props => props.color || 'var(--text-light)'};
  max-width: 700px;
  margin: 0 auto;

  ${props => props.alignment === 'left' && css`
    margin-left: 0;
  `}

  ${props => props.alignment === 'right' && css`
    margin-right: 0;
  `}

  @media (max-width: 992px) {
    font-size: 1.3rem;
    max-width: 600px;
  }

  @media (max-width: 768px) {
    font-size: var(--font-size-md);
    max-width: 500px;
  }

  @media (max-width: 576px) {
    font-size: 0.9rem;
    max-width: 100%;
  }
`;

const Section = ({
  title,
  subtitle,
  children,
  bgColor,
  bgImage,
  overlayOpacity,
  spacing,
  narrow,
  headerAlignment = 'center',
  titleColor,
  subtitleColor
}) => {
  return (
    <SectionContainer
      bgColor={bgColor}
      bgImage={bgImage}
      overlayOpacity={overlayOpacity}
      spacing={spacing}
    >
      <SectionContent narrow={narrow}>
        {(title || subtitle) && (
          <SectionHeader alignment={headerAlignment}>
            {title && <SectionTitle color={titleColor}>{title}</SectionTitle>}
            {subtitle && (
              <SectionSubtitle
                color={subtitleColor}
                alignment={headerAlignment}
              >
                {subtitle}
              </SectionSubtitle>
            )}
          </SectionHeader>
        )}
        {children}
      </SectionContent>
    </SectionContainer>
  );
};

export default Section;
