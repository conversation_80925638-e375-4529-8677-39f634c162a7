import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faStar,
  faPencilAlt,
  faGlobe,
  faLink,
  faEnvelope,
  faMapMarkerAlt,
  faClock,
  faBriefcase,
  faGraduationCap,
  faCode,
  faLightbulb,
  faUserCircle,
  faCalendarAlt
} from '@fortawesome/free-solid-svg-icons';
import { faLinkedin, faGithub, faFacebook, faTwitter, faInstagram } from '@fortawesome/free-brands-svg-icons';

import Button from '../../components/ui/Button';

// Styled Components
const ProfileContainer = styled.div`
  background-color: #f8f9fa;
  color: var(--text-color);
  min-height: calc(100vh - 80px);
  padding: 0;
  position: relative;
`;

const ProfileCover = styled.div`
  height: 300px;
  background: linear-gradient(to right, #004d40, #00796b);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1429&q=80') center/cover;
    opacity: 0.4;
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: flex-end;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  margin-top: -100px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
`;

const ProfileAvatar = styled.div`
  width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid #ffffff;
  margin-right: 2rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;

  @media (max-width: 768px) {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }
`;

const ProfileImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const ProfileInfo = styled.div`
  flex: 1;
  background-color: #ffffff;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  position: relative;
  z-index: 5;
`;

const ProfileName = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .verified-badge {
    color: var(--primary-color);
    font-size: 1.2rem;
  }
`;

const ProfileRating = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  svg {
    color: #ffb400;
    margin-right: 0.25rem;
  }

  span {
    margin-left: 0.5rem;
    color: #bdbdbd;
  }
`;

const ProfileActions = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-left: auto;

  @media (max-width: 768px) {
    margin-left: 0;
    margin-top: 1.5rem;
    justify-content: center;
  }
`;

const ProfileStats = styled.div`
  display: flex;
  gap: 2rem;
  margin-top: 1rem;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const StatItem = styled.div`
  text-align: center;

  .count {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
  }

  .label {
    font-size: 0.9rem;
    color: #bdbdbd;
    margin-top: 0.25rem;
  }
`;

const SocialIcon = styled.a`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-color);
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);

  &:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
  }
`;

const ProfileContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
  position: relative;
  z-index: 5;
`;

const Section = styled.section`
  margin-bottom: 3rem;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;

  svg {
    font-size: 1.25rem;
  }

  &:after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
  }
`;

const BioText = styled.p`
  color: var(--text-color);
  line-height: 1.6;
  max-width: 800px;
  margin-bottom: 1rem;
`;

const SkillsGrid = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
`;

const SkillTag = styled.div`
  background-color: #f1f1f1;
  color: var(--dark-color);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s ease, background-color 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    background-color: #e0e0e0;
  }
`;

const EducationCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const EducationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const EducationTitle = styled.h3`
  font-size: 1.2rem;
  color: #212121;
`;

const EducationDate = styled.span`
  color: #757575;
  font-size: 0.9rem;

  @media (max-width: 768px) {
    margin-top: 0.5rem;
  }
`;

const EducationSubtitle = styled.div`
  color: #757575;
  margin-bottom: 1rem;
`;

const EducationDetails = styled.div`
  margin-top: 1rem;
  color: #424242;
`;

const EducationSkills = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
`;

const EducationSkillTag = styled.span`
  background-color: #e0e0e0;
  color: #424242;
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.8rem;
  transition: all 0.2s ease;

  &:hover {
    background-color: #bdbdbd;
  }
`;

const ActionButton = styled(Button)`
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 50px;
  padding: 0.6rem 1.2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  font-weight: 600;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  }
`;

const EditButton = styled(ActionButton)`
  background-color: #f1f1f1;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: #e0e0e0;
  }
`;

const MessageButton = styled(ActionButton)`
  background-color: #f1f1f1;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: #e0e0e0;
  }
`;

const ScheduleButton = styled(ActionButton)`
  background-color: #f1f1f1;
  color: var(--dark-color);
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: #e0e0e0;
  }
`;

// Sample user data
const users = [
  {
    id: 'ayesha12',
    name: 'Ayesha Malik',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    rating: 5,
    ratingCount: 5,
    email: '<EMAIL>',
    username: 'ayesha12',
    linkedinLink: 'https://linkedin.com/in/ayesha-malik',
    githubLink: 'https://github.com/ayesha12',
    portfolioLink: 'https://ayesha-malik.dev',
    bio: 'Computer Science student specialising in data science and machine learning',
    currentStatus: 'Student',
    state: 'Islamabad',
    country: 'Pakistan',
    timezone: 'UTC+05:00',
    skillsToTeach: ['Machine Learning', 'Python', 'Data Science', 'SQL', 'React JS'],
    skillsToLearn: ['UI Design', 'JavaScript', 'React', 'Node.js'],
    education: [
      {
        institution: 'National University of Sciences and Technology (NUST), Islamabad',
        degree: 'Computer Science',
        majorField: 'Computer Science Engineering',
        startDate: 'September 2021',
        endDate: 'June 2025',
        details: 'Pursuing degree in Computer Science with specialization in AI and Machine Learning.',
        skills: ['HTML', 'CSS', 'JavaScript', 'Python', 'Data Structures']
      }
    ]
  },
  {
    id: 'ahmad42',
    name: 'Ahmad Khan',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    rating: 4,
    ratingCount: 8,
    email: '<EMAIL>',
    username: 'ahmad42',
    linkedinLink: 'https://linkedin.com/in/ahmad-khan',
    githubLink: 'https://github.com/ahmadkhan',
    portfolioLink: 'https://ahmad-khan.edu',
    bio: 'Professor - Mathematics at LUMS Lahore. Specialising in Algebra and Statistics',
    currentStatus: 'Employed',
    state: 'Lahore',
    country: 'Pakistan',
    timezone: 'UTC+05:00',
    skillsToTeach: ['Mathematics', 'Algebra', 'Arithmetic', 'Statistics'],
    skillsToLearn: ['Python', 'Data Science', 'Machine Learning'],
    education: [
      {
        institution: 'Lahore University of Management Sciences (LUMS)',
        degree: 'Ph.D',
        majorField: 'Applied Mathematics',
        startDate: 'July 2010',
        endDate: 'May 2015',
        details: 'PhD in Applied Mathematics with focus on algebraic structures and statistical analysis.',
        skills: ['Algebra', 'Calculus', 'Statistics', 'Mathematical Modeling']
      }
    ]
  }
];

const Profile = () => {
  const { userId } = useParams();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editSection, setEditSection] = useState(null);
  const [formData, setFormData] = useState({});

  useEffect(() => {
    // In a real app, this would be an API call
    // If no userId is provided, show the current user's profile
    if (!userId && currentUser) {
      // Check if the current user has registration data
      if (currentUser.email && currentUser.name) {
        // Use the current user's data directly from auth context
        // This will include all the registration form data
        setUser(currentUser);
        setFormData(currentUser);
      } else {
        // Fallback to sample data if user doesn't have complete profile
        const currentUserData = {
          ...users[0], // Using sample data for now
          id: currentUser.username,
          name: currentUser.name,
          username: currentUser.username
        };
        setUser(currentUserData);
        setFormData(currentUserData);
      }
    } else if (userId) {
      // If userId is provided, find that user
      const foundUser = users.find(u => u.id === userId) || users[0];
      setUser(foundUser);
      setFormData(foundUser);
    } else {
      // Default case: use the first user as a fallback
      setUser(users[0]);
      setFormData(users[0]);
    }
  }, [userId, currentUser]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditSection = (section) => {
    setEditSection(section);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditSection(null);
    setFormData(user); // Reset form data to current user data
  };

  const handleSaveEdit = () => {
    // In a real app, this would be an API call to update the user data
    setUser(formData);
    setIsEditing(false);
    setEditSection(null);
  };

  const isCurrentUserProfile = currentUser && (!userId || userId === currentUser.username);

  return (
    <ProfileContainer>
      <ProfileCover />

      <ProfileHeader>
        <ProfileAvatar>
          <ProfileImage src={user.avatar} alt={user.name} />
        </ProfileAvatar>

        <ProfileInfo>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <div>
              <ProfileName>
                {user.name}
                <span className="verified-badge" title="Verified User">✓</span>
              </ProfileName>
              <ProfileRating>
                {[...Array(5)].map((_, i) => (
                  <FontAwesomeIcon
                    key={i}
                    icon={faStar}
                    style={{ opacity: i < user.rating ? 1 : 0.3 }}
                  />
                ))}
                <span>({user.ratingCount})</span>
              </ProfileRating>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '0.5rem', color: '#bdbdbd', fontSize: '0.9rem' }}>
                <div>
                  <FontAwesomeIcon icon={faUserCircle} style={{ marginRight: '0.5rem' }} />
                  @{user.username}
                </div>
                <div>
                  <FontAwesomeIcon icon={faMapMarkerAlt} style={{ marginRight: '0.5rem' }} />
                  {user.state}, {user.country}
                </div>
              </div>
            </div>

            <SocialLinks>
              {user.githubLink && (
                <SocialIcon href={user.githubLink} target="_blank" rel="noopener noreferrer">
                  <FontAwesomeIcon icon={faGithub} />
                </SocialIcon>
              )}
              {user.linkedinLink && (
                <SocialIcon href={user.linkedinLink} target="_blank" rel="noopener noreferrer">
                  <FontAwesomeIcon icon={faLinkedin} />
                </SocialIcon>
              )}
              {user.portfolioLink && (
                <SocialIcon href={user.portfolioLink} target="_blank" rel="noopener noreferrer">
                  <FontAwesomeIcon icon={faLink} />
                </SocialIcon>
              )}
            </SocialLinks>
          </div>

          <ProfileStats>
            <StatItem>
              <div className="count">125</div>
              <div className="label">Connections</div>
            </StatItem>
            <StatItem>
              <div className="count">48</div>
              <div className="label">Skills Taught</div>
            </StatItem>
            <StatItem>
              <div className="count">32</div>
              <div className="label">Skills Learned</div>
            </StatItem>
          </ProfileStats>

          <ProfileActions>
            {isCurrentUserProfile ? (
              <EditButton onClick={() => handleEditSection('profile')}>
                <FontAwesomeIcon icon={faPencilAlt} />
                Edit Profile
              </EditButton>
            ) : (
              <>
                <MessageButton as={Link} to={`/messages/${user.id}`}>
                  <FontAwesomeIcon icon={faEnvelope} />
                  Message
                </MessageButton>
                <ScheduleButton as={Link} to={`/schedule/${user.id}`}>
                  <FontAwesomeIcon icon={faCalendarAlt} />
                  Schedule
                </ScheduleButton>
                <Button
                  style={{
                    backgroundColor: '#f1f1f1',
                    color: 'var(--dark-color)',
                    border: '1px solid #e0e0e0',
                    padding: '0.6rem 1.2rem',
                    borderRadius: '50px',
                    fontWeight: 'bold',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.backgroundColor = '#e0e0e0';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.backgroundColor = '#f1f1f1';
                  }}
                >
                  Connect
                </Button>
                <Button
                  style={{
                    backgroundColor: '#f1f1f1',
                    color: 'var(--dark-color)',
                    border: '1px solid #e0e0e0',
                    padding: '0.6rem 1.2rem',
                    borderRadius: '50px',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = '#e0e0e0';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = '#f1f1f1';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  Report
                </Button>
              </>
            )}
          </ProfileActions>
        </ProfileInfo>

        {isCurrentUserProfile && (
          <EditButton
            style={{ position: 'absolute', top: '1rem', right: '2rem' }}
            onClick={() => handleEditSection('profile')}
          >
            <FontAwesomeIcon icon={faPencilAlt} />
            Edit Profile
          </EditButton>
        )}
      </ProfileHeader>

      <ProfileContent>
        <Section>
          <SectionTitle>
            <FontAwesomeIcon icon={faUserCircle} />
            About
          </SectionTitle>
          <BioText>{user.bio}</BioText>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem', marginTop: '1.5rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <FontAwesomeIcon icon={faBriefcase} style={{ color: 'var(--primary-color)', fontSize: '1.25rem' }} />
              <div>
                <h4 style={{ color: 'var(--primary-color)', marginBottom: '0.5rem' }}>Current Status</h4>
                <p style={{ color: 'var(--text-color)' }}>{user.currentStatus}</p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <FontAwesomeIcon icon={faMapMarkerAlt} style={{ color: 'var(--primary-color)', fontSize: '1.25rem' }} />
              <div>
                <h4 style={{ color: 'var(--primary-color)', marginBottom: '0.5rem' }}>Location</h4>
                <p style={{ color: 'var(--text-color)' }}>{user.state}, {user.country}</p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <FontAwesomeIcon icon={faClock} style={{ color: 'var(--primary-color)', fontSize: '1.25rem' }} />
              <div>
                <h4 style={{ color: 'var(--primary-color)', marginBottom: '0.5rem' }}>Time Zone</h4>
                <p style={{ color: 'var(--text-color)' }}>{user.timezone}</p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <FontAwesomeIcon icon={faEnvelope} style={{ color: 'var(--primary-color)', fontSize: '1.25rem' }} />
              <div>
                <h4 style={{ color: 'var(--primary-color)', marginBottom: '0.5rem' }}>Contact</h4>
                <p style={{ color: 'var(--text-color)' }}>{user.email}</p>
              </div>
            </div>
          </div>
        </Section>

        <Section>
          <SectionTitle>
            <FontAwesomeIcon icon={faCode} />
            Skills to Teach
          </SectionTitle>
          <SkillsGrid>
            {user.skillsToTeach.map((skill, index) => (
              <SkillTag key={index} style={{ backgroundColor: '#f1f1f1', color: 'var(--dark-color)', border: '1px solid #e0e0e0' }}>{skill}</SkillTag>
            ))}
          </SkillsGrid>
        </Section>

        <Section>
          <SectionTitle>
            <FontAwesomeIcon icon={faLightbulb} />
            Skills to Learn
          </SectionTitle>
          <SkillsGrid>
            {user.skillsToLearn.map((skill, index) => (
              <SkillTag key={index} style={{ backgroundColor: '#f1f1f1', color: 'var(--dark-color)', border: '1px solid #e0e0e0' }}>{skill}</SkillTag>
            ))}
          </SkillsGrid>
        </Section>

        <Section>
          <SectionTitle>
            <FontAwesomeIcon icon={faGraduationCap} />
            Education
          </SectionTitle>
          {user.education && user.education.length > 0 ? (
            // Display education from user.education array if it exists
            user.education.map((edu, index) => (
              <EducationCard key={index}>
                <EducationHeader>
                  <EducationTitle>{edu.institution}</EducationTitle>
                  <EducationDate>{edu.startDate} - {edu.endDate}</EducationDate>
                </EducationHeader>
                <EducationSubtitle>{edu.degree} in {edu.majorField}</EducationSubtitle>
                <EducationDetails>{edu.details}</EducationDetails>
                <EducationSkills>
                  <strong>Skills Used:</strong>
                  {edu.skills && edu.skills.map((skill, i) => (
                    <EducationSkillTag key={i}>{skill}</EducationSkillTag>
                  ))}
                </EducationSkills>
              </EducationCard>
            ))
          ) : (
            // Display education from registration form data if education array doesn't exist
            user.educationLevel && (
              <EducationCard>
                <EducationHeader>
                  <EducationTitle>{user.universityName || 'University'}</EducationTitle>
                </EducationHeader>
                <EducationSubtitle>{user.educationLevel} in {user.majorFieldOfStudy}</EducationSubtitle>
                <EducationDetails>{user.educationDescription || 'No additional details provided.'}</EducationDetails>
              </EducationCard>
            )
          )}
        </Section>

        <Section>
          <SectionTitle>Links</SectionTitle>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
            {user.linkedinLink && (
              <a href={user.linkedinLink} target="_blank" rel="noopener noreferrer" style={{
                textDecoration: 'none',
                color: 'var(--text-color)',
                backgroundColor: '#ffffff',
                padding: '1rem',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)',
                border: '1px solid var(--border-color)'
              }} onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
                e.currentTarget.style.transform = 'translateY(-3px)';
                e.currentTarget.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.1)';
              }} onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
              }}>
                <FontAwesomeIcon icon={faLinkedin} size="lg" style={{ color: '#0077b5' }} />
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>LinkedIn</div>
                  <div style={{ fontSize: '0.85rem', opacity: 0.7 }}>View LinkedIn Profile</div>
                </div>
              </a>
            )}

            {user.githubLink && (
              <a href={user.githubLink} target="_blank" rel="noopener noreferrer" style={{
                textDecoration: 'none',
                color: 'var(--text-color)',
                backgroundColor: '#ffffff',
                padding: '1rem',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)',
                border: '1px solid var(--border-color)'
              }} onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
                e.currentTarget.style.transform = 'translateY(-3px)';
                e.currentTarget.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.1)';
              }} onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
              }}>
                <FontAwesomeIcon icon={faGithub} size="lg" />
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>GitHub</div>
                  <div style={{ fontSize: '0.85rem', opacity: 0.7 }}>View GitHub Profile</div>
                </div>
              </a>
            )}

            {user.portfolioLink && (
              <a href={user.portfolioLink} target="_blank" rel="noopener noreferrer" style={{
                textDecoration: 'none',
                color: 'var(--text-color)',
                backgroundColor: '#ffffff',
                padding: '1rem',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.05)',
                border: '1px solid var(--border-color)'
              }} onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
                e.currentTarget.style.transform = 'translateY(-3px)';
                e.currentTarget.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.1)';
              }} onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#ffffff';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.05)';
              }}>
                <FontAwesomeIcon icon={faLink} size="lg" style={{ color: 'var(--primary-color)' }} />
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Portfolio</div>
                  <div style={{ fontSize: '0.85rem', opacity: 0.7 }}>View Portfolio Website</div>
                </div>
              </a>
            )}
          </div>
        </Section>
      </ProfileContent>
    </ProfileContainer>
  );
};

export default Profile;
