import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import GlobalStyles from './styles/GlobalStyles';
import theme from './styles/theme';

// Layout
import Layout from './components/layout/Layout';
import AdminLayout from './components/admin';

// Pages
import Home from './pages/home';
import About from './pages/about';
import Services from './pages/services';
import Discover from './pages/discover';
import Contact from './pages/contact';
import Profile from './pages/profile';
import Register from './pages/register';
import Login from './pages/login';
import CreateAccount from './pages/create-account';
import Messages from './pages/messages';
import Schedule from './pages/schedule';
import UserSettings from './pages/settings';
import UserDashboard from './pages/dashboard';

// Admin Pages
import { Users, Skills, Exchanges } from './pages/admin';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/create-account" element={<CreateAccount />} />

            {/* Redirect root to login */}
            <Route path="/" element={<Navigate to="/login" replace />} exact />

            {/* Main Routes with Layout */}
            <Route element={<Layout />}>
              <Route path="/home" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/services" element={<Services />} />
              <Route path="/discover" element={<Discover />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/dashboard" element={<UserDashboard />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/profile/:userId" element={<Profile />} />
              <Route path="/register" element={<Register />} />
              <Route path="/messages" element={<Messages />} />
              <Route path="/messages/:userId" element={<Messages />} />
              <Route path="/schedule" element={<Schedule />} />
              <Route path="/schedule/:userId" element={<Schedule />} />
              <Route path="/settings" element={<UserSettings />} />
            </Route>

            {/* Admin Routes */}
            <Route path="/admin" element={<AdminLayout />}>
              <Route index element={<Users />} />
              <Route path="users" element={<Users />} />
              <Route path="skills" element={<Skills />} />
              <Route path="exchanges" element={<Exchanges />} />
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
