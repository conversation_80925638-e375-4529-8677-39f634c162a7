import styled, { css } from 'styled-components';

const CardContainer = styled.div`
  background-color: var(--light-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  ${props => props.hoverable && css`
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
  `}
  
  ${props => props.bordered && css`
    border: 1px solid var(--border-color);
  `}
`;

const CardImage = styled.div`
  width: 100%;
  height: 200px;
  background-image: url(${props => props.src});
  background-size: cover;
  background-position: center;
`;

const CardContent = styled.div`
  padding: var(--spacing-lg);
`;

const CardTitle = styled.h3`
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--dark-color);
`;

const CardDescription = styled.p`
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
`;

const CardFooter = styled.div`
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Card = ({ 
  title, 
  description, 
  imageSrc, 
  footer, 
  children, 
  hoverable = false,
  bordered = false,
  ...props 
}) => {
  return (
    <CardContainer hoverable={hoverable} bordered={bordered} {...props}>
      {imageSrc && <CardImage src={imageSrc} />}
      <CardContent>
        {title && <CardTitle>{title}</CardTitle>}
        {description && <CardDescription>{description}</CardDescription>}
        {children}
      </CardContent>
      {footer && <CardFooter>{footer}</CardFooter>}
    </CardContainer>
  );
};

export default Card;
