import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock, faUser, faBell, faShield } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';

const SettingsContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
`;

const SettingsHeader = styled.div`
  margin-bottom: 2rem;
`;

const SettingsTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const SettingsDescription = styled.p`
  color: var(--text-light);
`;

const SettingsLayout = styled.div`
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SettingsSidebar = styled.div`
  background-color: var(--light-color);
  border-radius: 8px;
  overflow: hidden;
`;

const SidebarItem = styled.div`
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: ${props => props.active ? 'var(--primary-color)' : 'transparent'};
  color: ${props => props.active ? 'white' : 'var(--text-color)'};
  
  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(0, 0, 0, 0.05)'};
  }
`;

const SettingsContent = styled.div`
  background-color: var(--light-color);
  border-radius: 8px;
  padding: 1.5rem;
`;

const SettingSection = styled.div`
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(106, 61, 232, 0.2);
  }
`;

const Button = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #5a3cc0;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  margin-top: 0.5rem;
  font-size: 0.875rem;
`;

const SuccessMessage = styled.div`
  color: #28a745;
  margin-top: 0.5rem;
  font-size: 0.875rem;
`;

const Settings = () => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('password');
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setError('Please fill in all fields');
      return;
    }
    
    if (formData.newPassword !== formData.confirmPassword) {
      setError('New passwords do not match');
      return;
    }
    
    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }
    
    // Here you would typically call an API to change the password
    // For now, we'll just simulate success
    setSuccess('Password updated successfully');
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  };
  
  return (
    <SettingsContainer>
      <SettingsHeader>
        <SettingsTitle>Account Settings</SettingsTitle>
        <SettingsDescription>Manage your account settings and preferences</SettingsDescription>
      </SettingsHeader>
      
      <SettingsLayout>
        <SettingsSidebar>
          <SidebarItem 
            active={activeTab === 'profile'} 
            onClick={() => setActiveTab('profile')}
          >
            <FontAwesomeIcon icon={faUser} />
            Profile Information
          </SidebarItem>
          <SidebarItem 
            active={activeTab === 'password'} 
            onClick={() => setActiveTab('password')}
          >
            <FontAwesomeIcon icon={faLock} />
            Password
          </SidebarItem>
          <SidebarItem 
            active={activeTab === 'notifications'} 
            onClick={() => setActiveTab('notifications')}
          >
            <FontAwesomeIcon icon={faBell} />
            Notifications
          </SidebarItem>
          <SidebarItem 
            active={activeTab === 'privacy'} 
            onClick={() => setActiveTab('privacy')}
          >
            <FontAwesomeIcon icon={faShield} />
            Privacy & Security
          </SidebarItem>
        </SettingsSidebar>
        
        <SettingsContent>
          {activeTab === 'password' && (
            <SettingSection>
              <SectionTitle>Change Password</SectionTitle>
              <form onSubmit={handleSubmit}>
                <FormGroup>
                  <FormLabel htmlFor="currentPassword">Current Password</FormLabel>
                  <FormInput 
                    type="password" 
                    id="currentPassword" 
                    name="currentPassword" 
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="newPassword">New Password</FormLabel>
                  <FormInput 
                    type="password" 
                    id="newPassword" 
                    name="newPassword" 
                    value={formData.newPassword}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="confirmPassword">Confirm New Password</FormLabel>
                  <FormInput 
                    type="password" 
                    id="confirmPassword" 
                    name="confirmPassword" 
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                  />
                </FormGroup>
                
                {error && <ErrorMessage>{error}</ErrorMessage>}
                {success && <SuccessMessage>{success}</SuccessMessage>}
                
                <Button type="submit">Update Password</Button>
              </form>
            </SettingSection>
          )}
          
          {activeTab === 'profile' && (
            <SettingSection>
              <SectionTitle>Profile Information</SectionTitle>
              <p>This section will allow you to update your profile information.</p>
            </SettingSection>
          )}
          
          {activeTab === 'notifications' && (
            <SettingSection>
              <SectionTitle>Notification Preferences</SectionTitle>
              <p>This section will allow you to manage your notification settings.</p>
            </SettingSection>
          )}
          
          {activeTab === 'privacy' && (
            <SettingSection>
              <SectionTitle>Privacy & Security</SectionTitle>
              <p>This section will allow you to manage your privacy and security settings.</p>
            </SettingSection>
          )}
        </SettingsContent>
      </SettingsLayout>
    </SettingsContainer>
  );
};

export default Settings;
