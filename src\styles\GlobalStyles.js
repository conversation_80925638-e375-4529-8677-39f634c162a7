import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  :root {
    /* New color scheme - different from the reference site */
    --primary-color: #7e57c2;     /* Purple as primary color */
    --primary-rgb: 126, 87, 194;  /* RGB values for primary color */
    --primary-dark: #5e35b1;     /* Darker purple for hover effects */
    --secondary-color: #9575cd;   /* Lighter purple as secondary color */
    --secondary-rgb: 149, 117, 205; /* RGB values for secondary color */
    --secondary-dark: #7e57c2;   /* Darker secondary for hover effects */
    --accent-color: #e83d6a;      /* Pink as accent color */
    --background-color: #f8f9fa;
    --dark-color: #2d3748;
    --light-color: #ffffff;
    --text-color: #333333;
    --text-light: #666666;
    --border-color: #e2e8f0;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #e53e3e;

    /* Font sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 5rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Border radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-full: 9999px;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
  }

  a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;

    &:hover {
      color: var(--accent-color);
    }
  }

  button {
    cursor: pointer;
    font-family: inherit;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  p {
    margin-bottom: 1rem;
  }

  ul, ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  section {
    padding: var(--spacing-3xl) 0;
  }

  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
  }
`;

export default GlobalStyles;
