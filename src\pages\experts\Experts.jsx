import { useState } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faStar,
  faFilter,
  faGraduationCap,
  faCode,
  faPalette,
  faChartLine,
  faMusic,
  faLanguage,
  faHeartbeat,
  faUtensils
} from '@fortawesome/free-solid-svg-icons';

import Hero from '../../components/ui/Hero';
import Section from '../../components/ui/Section';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

// Import placeholder images
import images from '../../assets/placeholderImages';

const SearchContainer = styled.div`
  display: flex;
  margin-bottom: var(--spacing-2xl);

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const SearchInput = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  background-color: var(--light-color);
  border-radius: var(--border-radius-md);
  padding: 0 var(--spacing-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  svg {
    color: var(--text-light);
    margin-right: var(--spacing-sm);
  }

  input {
    flex: 1;
    border: none;
    padding: var(--spacing-md);
    font-size: var(--font-size-md);

    &:focus {
      outline: none;
    }
  }

  @media (max-width: 768px) {
    margin-bottom: var(--spacing-md);
  }
`;

const FilterButton = styled(Button)`
  margin-left: var(--spacing-md);

  @media (max-width: 768px) {
    margin-left: 0;
  }
`;

const CategoriesContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-2xl);
`;

const CategoryButton = styled.button`
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: ${props => props.active ? 'var(--primary-color)' : 'var(--light-color)'};
  color: ${props => props.active ? 'white' : 'var(--text-color)'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : 'var(--border-color)'};
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;

  svg {
    margin-right: var(--spacing-sm);
  }

  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(106, 61, 232, 0.1)'};
    border-color: var(--primary-color);
  }
`;

const ExpertsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const ExpertCard = styled(Card)`
  display: flex;
  flex-direction: column;
`;

const ExpertImage = styled.img`
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md);
`;

const ExpertInfo = styled.div`
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const ExpertName = styled.h3`
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-xs);
`;

const ExpertTitle = styled.p`
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
`;

const ExpertRating = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);

  svg {
    color: #FFD700;
    margin-right: var(--spacing-xs);
  }

  span {
    margin-left: var(--spacing-xs);
    color: var(--text-light);
  }
`;

const ExpertTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
`;

const ExpertTag = styled.span`
  background-color: rgba(106, 61, 232, 0.1);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
`;

const ExpertActions = styled.div`
  margin-top: auto;
  display: flex;
  gap: var(--spacing-sm);
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-2xl);
`;

const PageButton = styled.button`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  margin: 0 var(--spacing-xs);
  background-color: ${props => props.active ? 'var(--primary-color)' : 'var(--light-color)'};
  color: ${props => props.active ? 'white' : 'var(--text-color)'};
  border: 1px solid ${props => props.active ? 'var(--primary-color)' : 'var(--border-color)'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(106, 61, 232, 0.1)'};
    border-color: var(--primary-color);
  }
`;

const categories = [
  { id: 'all', name: 'All Categories', icon: faFilter },
  { id: 'programming', name: 'Programming', icon: faCode },
  { id: 'design', name: 'Design', icon: faPalette },
  { id: 'business', name: 'Business', icon: faChartLine },
  { id: 'music', name: 'Music', icon: faMusic },
  { id: 'language', name: 'Languages', icon: faLanguage },
  { id: 'health', name: 'Health & Fitness', icon: faHeartbeat },
  { id: 'cooking', name: 'Cooking', icon: faUtensils },
  { id: 'education', name: 'Education', icon: faGraduationCap },
];

const experts = [
  {
    id: 1,
    name: 'John Smith',
    title: 'Senior Web Developer',
    image: images.expert1,
    rating: 4.9,
    reviews: 128,
    tags: ['JavaScript', 'React', 'Node.js'],
    category: 'programming'
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    title: 'UX/UI Designer',
    image: images.expert2,
    rating: 4.8,
    reviews: 95,
    tags: ['UI Design', 'User Research', 'Figma'],
    category: 'design'
  },
  {
    id: 3,
    name: 'Emily Chen',
    title: 'Marketing Specialist',
    image: images.expert3,
    rating: 4.7,
    reviews: 87,
    tags: ['Digital Marketing', 'SEO', 'Social Media'],
    category: 'business'
  },
  {
    id: 4,
    name: 'Michael Rodriguez',
    title: 'Music Producer',
    image: images.expert4,
    rating: 4.9,
    reviews: 112,
    tags: ['Music Production', 'Piano', 'Composition'],
    category: 'music'
  },
  {
    id: 5,
    name: 'Jessica Kim',
    title: 'Language Teacher',
    image: images.expert5,
    rating: 4.8,
    reviews: 76,
    tags: ['Spanish', 'French', 'ESL'],
    category: 'language'
  },
  {
    id: 6,
    name: 'David Wilson',
    title: 'Fitness Coach',
    image: images.expert6,
    rating: 4.9,
    reviews: 143,
    tags: ['Fitness', 'Nutrition', 'Yoga'],
    category: 'health'
  },
];

const Experts = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [activePage, setActivePage] = useState(1);

  const filteredExperts = experts.filter(expert => {
    // Filter by category
    if (activeCategory !== 'all' && expert.category !== activeCategory) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        expert.name.toLowerCase().includes(query) ||
        expert.title.toLowerCase().includes(query) ||
        expert.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return true;
  });

  return (
    <>
      <Hero
        bgImage={images.heroExperts}
        title="Find an Expert"
        subtitle="Connect with skilled professionals who can help you learn and grow."
        height="60vh"
      />

      <Section>
        <SearchContainer>
          <SearchInput>
            <FontAwesomeIcon icon={faSearch} />
            <input
              type="text"
              placeholder="Search for experts by name, skill, or keyword..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </SearchInput>
          <FilterButton variant="outline">
            <FontAwesomeIcon icon={faFilter} style={{ marginRight: 'var(--spacing-sm)' }} />
            Filters
          </FilterButton>
        </SearchContainer>

        <CategoriesContainer>
          {categories.map(category => (
            <CategoryButton
              key={category.id}
              active={activeCategory === category.id}
              onClick={() => setActiveCategory(category.id)}
            >
              <FontAwesomeIcon icon={category.icon} />
              {category.name}
            </CategoryButton>
          ))}
        </CategoriesContainer>

        <ExpertsGrid>
          {filteredExperts.map(expert => (
            <ExpertCard key={expert.id} hoverable>
              <ExpertImage src={expert.image} alt={expert.name} />
              <ExpertInfo>
                <ExpertName>{expert.name}</ExpertName>
                <ExpertTitle>{expert.title}</ExpertTitle>
                <ExpertRating>
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <FontAwesomeIcon icon={faStar} />
                  <strong>{expert.rating}</strong>
                  <span>({expert.reviews} reviews)</span>
                </ExpertRating>
                <ExpertTags>
                  {expert.tags.map((tag, index) => (
                    <ExpertTag key={index}>{tag}</ExpertTag>
                  ))}
                </ExpertTags>
                <ExpertActions>
                  <Button fullWidth>View Profile</Button>
                  <Button variant="outline" fullWidth>Book Session</Button>
                </ExpertActions>
              </ExpertInfo>
            </ExpertCard>
          ))}
        </ExpertsGrid>

        <Pagination>
          <PageButton onClick={() => setActivePage(1)} active={activePage === 1}>1</PageButton>
          <PageButton onClick={() => setActivePage(2)} active={activePage === 2}>2</PageButton>
          <PageButton onClick={() => setActivePage(3)} active={activePage === 3}>3</PageButton>
          <PageButton>...</PageButton>
          <PageButton onClick={() => setActivePage(10)} active={activePage === 10}>10</PageButton>
        </Pagination>
      </Section>

      <Section
        bgColor="var(--primary-color)"
        spacing="lg"
      >
        <div style={{ textAlign: 'center', maxWidth: '700px', margin: '0 auto' }}>
          <h2 style={{ color: 'white', marginBottom: 'var(--spacing-lg)' }}>Become an Expert on SkillSwap</h2>
          <p style={{ color: 'white', marginBottom: 'var(--spacing-xl)' }}>
            Share your knowledge, help others learn, and earn money by becoming an expert on our platform.
          </p>
          <Button
            variant="secondary"
            size="large"
          >
            Apply Now
          </Button>
        </div>
      </Section>
    </>
  );
};

export default Experts;
